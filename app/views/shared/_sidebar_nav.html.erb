<%
  # Enhanced navigation structure with better organization and visual hierarchy
  nav_sections = [
    {
      title: "Main",
      items: [
        {
          name: 'Dashboard',
          path: authenticated_root_path,
          icon: 'dashboard',
          description: 'Overview & insights',
          badge: nil
        },
        {
          name: 'Links',
          path: links_path,
          icon: 'link',
          description: 'Manage your links',
          badge: current_user.links.count
        },
        {
          name: 'Analytics',
          path: analytics_path,
          icon: 'analytics',
          description: 'Performance metrics',
          badge: nil
        }
      ]
    },
    {
      title: "Tools",
      items: [
        {
          name: 'QR Codes',
          path: '#',
          icon: 'qr-code',
          description: 'Generate QR codes',
          badge: nil
        },
        {
          name: 'Bulk Import',
          path: '#',
          icon: 'upload',
          description: 'Import multiple links',
          badge: nil
        }
      ]
    }
  ]

  current_path = request.path
%>

<!-- Enhanced Navigation Sections -->
<% nav_sections.each_with_index do |section, section_index| %>
  <% unless section_index == 0 %>
    <!-- Section Divider -->
    <div class="my-4">
      <div class="relative">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-gray-200"></div>
        </div>
        <div class="relative flex justify-center">
          <span class="bg-white px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider"><%= section[:title] %></span>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Navigation Items -->
  <% section[:items].each do |item| %>
    <%
      is_active = case item[:name]
      when 'Dashboard'
        current_path == authenticated_root_path || current_path == '/dashboard'
      when 'Links'
        current_path.start_with?('/links')
      when 'Analytics'
        current_path.start_with?('/analytics')
      else
        false
      end

      # Enhanced styling based on active state
      base_classes = "group relative flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 mb-1"
      active_classes = "bg-gradient-to-r from-purple-50 to-purple-100 text-purple-700 shadow-sm border border-purple-200"
      inactive_classes = "text-gray-700 hover:bg-gray-50 hover:text-gray-900"

      link_classes = "#{base_classes} #{is_active ? active_classes : inactive_classes}"
    %>

    <%= link_to item[:path], class: link_classes, title: item[:name] do %>
      <!-- Icon with enhanced styling -->
      <div class="flex-shrink-0 w-5 h-5 lg:mr-3 relative">
        <svg class="w-5 h-5 <%= is_active ? 'text-purple-600' : 'text-gray-400 group-hover:text-gray-600' %> transition-colors"
             fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <% case item[:icon] %>
          <% when 'dashboard' %>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          <% when 'link' %>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
          <% when 'analytics' %>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
          <% when 'qr-code' %>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h2M4 12h4m12 0h.01M4 20h2m2-2v-6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v6M7 8V6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v2"></path>
          <% when 'upload' %>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          <% end %>
        </svg>
      </div>

      <!-- Text Content -->
      <div class="hidden lg:block flex-1 min-w-0">
        <div class="flex items-center justify-between">
          <div class="min-w-0 flex-1">
            <p class="font-semibold truncate"><%= item[:name] %></p>
            <p class="text-xs <%= is_active ? 'text-purple-600' : 'text-gray-500' %> truncate"><%= item[:description] %></p>
          </div>

          <!-- Badge -->
          <% if item[:badge] && item[:badge] > 0 %>
            <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium <%= is_active ? 'bg-purple-200 text-purple-800' : 'bg-gray-200 text-gray-800' %>">
              <%= item[:badge] %>
            </span>
          <% end %>
        </div>
      </div>

      <!-- Active Indicator -->
      <% if is_active %>
        <div class="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-purple-500 to-purple-600 rounded-r-full"></div>
      <% end %>

      <!-- Hover Arrow -->
      <div class="hidden lg:block opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <svg class="w-4 h-4 <%= is_active ? 'text-purple-600' : 'text-gray-400' %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </div>
    <% end %>
  <% end %>
<% end %>

<!-- Settings & Administration Section -->
<%
  settings_section = {
    title: "Settings",
    items: [
      {
        name: 'Account',
        path: settings_path,
        icon: 'settings',
        description: 'Profile & preferences',
        badge: nil
      },
      {
        name: 'Team',
        path: settings_path(section: 'team'),
        icon: 'users',
        description: 'Manage team members',
        badge: nil
      },
      {
        name: 'API Tokens',
        path: settings_path(section: 'api_tokens'),
        icon: 'key',
        description: 'API access keys',
        badge: nil
      },
      {
        name: 'Domains',
        path: settings_path(section: 'domains'),
        icon: 'globe',
        description: 'Custom domains',
        badge: nil
      }
    ]
  }

  support_section = {
    title: "Support",
    items: [
      {
        name: 'Billing',
        path: settings_path(section: 'billing'),
        icon: 'credit-card',
        description: 'Plans & payments',
        badge: nil
      },
      {
        name: 'Integrations',
        path: settings_path(section: 'integrations'),
        icon: 'puzzle',
        description: 'Third-party apps',
        badge: nil
      },
      {
        name: 'Help Center',
        path: help_path,
        icon: 'help',
        description: 'Documentation & support',
        badge: nil
      }
    ]
  }
%>

<!-- Settings Section -->
<div class="my-4">
  <div class="relative">
    <div class="absolute inset-0 flex items-center">
      <div class="w-full border-t border-gray-200"></div>
    </div>
    <div class="relative flex justify-center">
      <span class="bg-white px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider"><%= settings_section[:title] %></span>
    </div>
  </div>
</div>

<% settings_section[:items].each do |item| %>
  <%
    is_active = case item[:name]
    when 'Account'
      current_path == '/settings' && (params[:section].nil? || params[:section] == 'account')
    when 'API Tokens'
      current_path == '/settings' && params[:section] == 'api_tokens'
    when 'Domains'
      current_path == '/settings' && params[:section] == 'domains'
    when 'Team'
      current_path == '/settings' && params[:section] == 'team'
    else
      false
    end

    # Enhanced styling for settings items
    base_classes = "group relative flex items-center px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-200 mb-1"
    active_classes = "bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 shadow-sm border border-blue-200"
    inactive_classes = "text-gray-600 hover:bg-gray-50 hover:text-gray-900"

    link_classes = "#{base_classes} #{is_active ? active_classes : inactive_classes}"
  %>

  <%= link_to item[:path], class: link_classes, title: item[:name] do %>
    <!-- Icon -->
    <div class="flex-shrink-0 w-4 h-4 lg:mr-3">
      <svg class="w-4 h-4 <%= is_active ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600' %> transition-colors"
           fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <% case item[:icon] %>
        <% when 'settings' %>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        <% when 'users' %>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
        <% when 'key' %>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
        <% when 'globe' %>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
        <% when 'credit-card' %>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
        <% when 'puzzle' %>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"></path>
        <% when 'help' %>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        <% end %>
      </svg>
    </div>

    <!-- Text Content -->
    <div class="hidden lg:block flex-1 min-w-0">
      <p class="font-medium truncate"><%= item[:name] %></p>
      <p class="text-xs <%= is_active ? 'text-blue-600' : 'text-gray-500' %> truncate"><%= item[:description] %></p>
    </div>

    <!-- Active Indicator -->
    <% if is_active %>
      <div class="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-gradient-to-b from-blue-500 to-blue-600 rounded-r-full"></div>
    <% end %>
  <% end %>
<% end %>

<!-- Support Section -->
<div class="my-4">
  <div class="relative">
    <div class="absolute inset-0 flex items-center">
      <div class="w-full border-t border-gray-200"></div>
    </div>
    <div class="relative flex justify-center">
      <span class="bg-white px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider"><%= support_section[:title] %></span>
    </div>
  </div>
</div>

<% support_section[:items].each do |item| %>
  <%
    is_active = case item[:name]
    when 'Billing'
      current_path == '/settings' && params[:section] == 'billing'
    when 'Integrations'
      current_path == '/settings' && params[:section] == 'integrations'
    when 'Help Center'
      current_path.start_with?('/help')
    else
      false
    end

    # Styling for support items
    base_classes = "group relative flex items-center px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-200 mb-1"
    active_classes = "bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 shadow-sm border border-green-200"
    inactive_classes = "text-gray-600 hover:bg-gray-50 hover:text-gray-900"

    link_classes = "#{base_classes} #{is_active ? active_classes : inactive_classes}"
  %>

  <%= link_to item[:path], class: link_classes, title: item[:name] do %>
    <!-- Icon -->
    <div class="flex-shrink-0 w-4 h-4 lg:mr-3">
      <svg class="w-4 h-4 <%= is_active ? 'text-green-600' : 'text-gray-400 group-hover:text-gray-600' %> transition-colors"
           fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <% case item[:icon] %>
        <% when 'credit-card' %>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
        <% when 'puzzle' %>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"></path>
        <% when 'help' %>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        <% end %>
      </svg>
    </div>

    <!-- Text Content -->
    <div class="hidden lg:block flex-1 min-w-0">
      <p class="font-medium truncate"><%= item[:name] %></p>
      <p class="text-xs <%= is_active ? 'text-green-600' : 'text-gray-500' %> truncate"><%= item[:description] %></p>
    </div>

    <!-- Active Indicator -->
    <% if is_active %>
      <div class="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-gradient-to-b from-green-500 to-green-600 rounded-r-full"></div>
    <% end %>
  <% end %>
<% end %>