module Settings
  class BillingSettingsController < ApplicationController
    layout 'dashboard'
    before_action :authenticate_user!
  
  def show
    @user = current_user
    @subscription_plans = subscription_plans
    @current_plan = current_user_plan
    @usage_stats = calculate_usage_stats
  end

  def update
    @user = current_user
    
    # This would integrate with a payment processor like Stripe
    # For now, we'll just update the plan in the database
    if update_subscription_plan(billing_params[:plan])
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("billing-settings", partial: "billing_settings/form", locals: { user: @user, success: true }) }
        format.html { redirect_to settings_billing_settings_path, notice: 'Subscription updated successfully.' }
      end
    else
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("billing-settings", partial: "billing_settings/form", locals: { user: @user, error: "Failed to update subscription" }) }
        format.html { render :show, status: :unprocessable_entity }
      end
    end
  end

  private

  def subscription_plans
    [
      { id: 'free', name: 'Free', price: 0, links_limit: 1000, features: ['1,000 links/month', 'Basic analytics', 'Standard support'] },
      { id: 'professional', name: 'Professional', price: 9, links_limit: 10000, features: ['10,000 links/month', 'Advanced analytics', 'Custom domains', 'Priority support'] },
      { id: 'business', name: 'Business', price: 29, links_limit: 100000, features: ['100,000 links/month', 'Team collaboration', 'API access', 'Custom branding'] },
      { id: 'enterprise', name: 'Enterprise', price: 99, links_limit: Float::INFINITY, features: ['Unlimited links', 'White-label solution', 'SSO integration', 'Dedicated support'] }
    ]
  end

  def current_user_plan
    # This would come from a subscription model in production
    current_user.subscription_plan || 'free'
  end

  def calculate_usage_stats
    {
      links_created_this_month: current_user.links.where(created_at: Date.current.beginning_of_month..Date.current.end_of_month).count,
      total_clicks_this_month: current_user.links.joins(:link_clicks).where(link_clicks: { created_at: Date.current.beginning_of_month..Date.current.end_of_month }).count,
      api_requests_this_month: current_user.api_tokens.sum { |token| token.monthly_requests || 0 }
    }
  end

  def update_subscription_plan(plan_id)
    # In production, this would integrate with Stripe or similar
    # For now, just update a field on the user
    current_user.update(subscription_plan: plan_id)
  end

  def billing_params
    params.require(:billing).permit(:plan)
  end
  end
end